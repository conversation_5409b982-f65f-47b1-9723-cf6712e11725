#!/usr/bin/env python3
"""
Test script to verify the dependency flattening fix
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.intelligent_ai_service import IntelligentAIService
from app.models.project_models import IntelligentProjectRequest, AIProvider

def test_dependency_flattening():
    """Test the dependency flattening functionality"""
    
    service = IntelligentAIService()
    
    # Test case 1: Nested testing dependencies (the problematic case)
    nested_dependencies = {
        "frontend": ["react", "typescript", "vite"],
        "backend": ["fastapi", "uvicorn"],
        "testing": {
            "frontend": ["jest", "react-testing-library"],
            "backend": ["pytest", "pytest-asyncio", "httpx"]
        }
    }
    
    flattened = service._flatten_dependencies(nested_dependencies)
    print("Test case 1 - Nested testing dependencies:")
    print(f"Input: {nested_dependencies}")
    print(f"Output: {flattened}")
    print()
    
    # Test case 2: Already flat dependencies
    flat_dependencies = {
        "frontend": ["react", "typescript"],
        "backend": ["fastapi", "uvicorn"],
        "testing": ["vitest", "pytest"]
    }
    
    flattened2 = service._flatten_dependencies(flat_dependencies)
    print("Test case 2 - Already flat dependencies:")
    print(f"Input: {flat_dependencies}")
    print(f"Output: {flattened2}")
    print()
    
    # Test case 3: Mixed nested and flat
    mixed_dependencies = {
        "frontend": ["react", "typescript"],
        "backend": ["fastapi"],
        "testing": {
            "frontend": ["jest"],
            "backend": ["pytest"]
        },
        "deployment": ["vercel", "railway"]
    }
    
    flattened3 = service._flatten_dependencies(mixed_dependencies)
    print("Test case 3 - Mixed nested and flat:")
    print(f"Input: {mixed_dependencies}")
    print(f"Output: {flattened3}")
    print()
    
    # Verify that all outputs are valid Dict[str, List[str]]
    for i, result in enumerate([flattened, flattened2, flattened3], 1):
        print(f"Validation for test case {i}:")
        for key, value in result.items():
            if not isinstance(value, list):
                print(f"  ERROR: {key} is not a list: {type(value)}")
            elif not all(isinstance(item, str) for item in value):
                print(f"  ERROR: {key} contains non-string items: {value}")
            else:
                print(f"  OK: {key} = {value}")
        print()

if __name__ == "__main__":
    test_dependency_flattening()
