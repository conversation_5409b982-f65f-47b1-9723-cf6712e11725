# AI Project Generator

A clean, AI-driven project generator that creates complete project structures using OpenAI, Hugging Face, or Grok models.

## Features

- **AI-Powered Generation**: Uses AI to analyze requirements and generate complete projects
- **Multiple AI Providers**: Support for OpenAI, Hugging Face, and Grok models
- **Full-Stack Projects**: Generates React frontend and FastAPI backend
- **Real-time Progress**: Live updates during project generation
- **Project Management**: View, download, and manage generated projects

## Quick Start

1. **Setup Environment**:
```bash
sh setup.sh
```

2. **Configure API Keys** (add to `backend/.env`):
```env
OPENAI_API_KEY=your_openai_api_key_here
HUGGINGFACE_API_KEY=your_huggingface_api_key_here
GROK_API_KEY=your_grok_api_key_here
```

3. **Start Development Servers**:
```bash
sh start-dev.sh
```

**Access**:
- Frontend: http://localhost:5173
- Backend API: http://localhost:8000
- API Docs: http://localhost:8000/docs

## Usage

1. Enter project description
2. Select AI provider and complexity
3. Click "Generate Project"
4. Monitor real-time progress
5. Download completed project

## Tech Stack

**Frontend**: React 18 + Vite + Tailwind CSS
**Backend**: FastAPI + LangChain + Pydantic
**AI Integration**: OpenAI, Hugging Face, Grok APIs
