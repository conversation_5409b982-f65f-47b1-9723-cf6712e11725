import axios from "axios";

// Create axios instance with base configuration
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || "http://localhost:8000",
  timeout: 30000,
  headers: {
    "Content-Type": "application/json",
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add any auth headers here if needed
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    // Enhanced error logging for debugging
    console.error("API Error Details:", {
      message: error.message,
      code: error.code,
      config: {
        url: error.config?.url,
        method: error.config?.method,
        baseURL: error.config?.baseURL,
        timeout: error.config?.timeout,
      },
      response: error.response
        ? {
            status: error.response.status,
            statusText: error.response.statusText,
            data: error.response.data,
          }
        : null,
      request: error.request
        ? {
            readyState: error.request.readyState,
            status: error.request.status,
            responseURL: error.request.responseURL,
          }
        : null,
    });

    // Handle common errors
    if (error.response) {
      // Server responded with error status
      const message =
        error.response.data?.detail ||
        error.response.data?.message ||
        `Server error: ${error.response.status} ${error.response.statusText}`;
      throw new Error(message);
    } else if (error.request) {
      // Request was made but no response received
      console.error("No response from server:", error.request);

      // Check for specific network errors
      if (error.code === "ECONNREFUSED") {
        throw new Error(
          "Connection refused. Is the server running on http://localhost:8000?"
        );
      } else if (error.code === "NETWORK_ERROR") {
        throw new Error(
          "Network error. Please check your internet connection."
        );
      } else if (error.code === "ENOTFOUND") {
        throw new Error("Server not found. Please check the server URL.");
      } else {
        throw new Error(
          `No response from server. Error: ${
            error.code || "Unknown"
          }. Please check your connection and ensure the server is running.`
        );
      }
    } else {
      // Something else happened
      throw new Error(error.message || "An unexpected error occurred");
    }
  }
);

// API functions
export const generateIntelligentProject = async (projectData) => {
  try {
    const response = await api.post("/api/generate/intelligent", projectData);
    return response;
  } catch (error) {
    throw error;
  }
};

export const getEnhancedProjectStatus = async (projectId) => {
  try {
    const response = await api.get(`/api/status/enhanced/${projectId}`);
    return response;
  } catch (error) {
    throw error;
  }
};

export const getProjectAnalysis = async (projectId) => {
  try {
    const response = await api.get(`/api/analysis/${projectId}`);
    return response;
  } catch (error) {
    throw error;
  }
};

export const getModificationSuggestions = async (projectId) => {
  try {
    const response = await api.get(`/api/suggestions/${projectId}`);
    return response;
  } catch (error) {
    throw error;
  }
};

export const requestModifications = async (projectId, modificationData) => {
  try {
    const response = await api.post(
      `/api/modify/${projectId}`,
      modificationData
    );
    return response;
  } catch (error) {
    throw error;
  }
};

export const applyModifications = async (projectId, applyData) => {
  try {
    const response = await api.post(
      `/api/apply-modifications/${projectId}`,
      applyData
    );
    return response;
  } catch (error) {
    throw error;
  }
};

export const downloadProject = async (projectId) => {
  try {
    const response = await axios({
      method: "GET",
      url: `${api.defaults.baseURL}/api/download/${projectId}`,
      responseType: "blob",
      timeout: 60000, // 1 minute timeout for downloads
    });

    // Create blob link to download
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement("a");
    link.href = url;

    // Try to get filename from response headers
    const contentDisposition = response.headers["content-disposition"];
    let filename = `project_${projectId}.zip`;

    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename="(.+)"/);
      if (filenameMatch) {
        filename = filenameMatch[1];
      }
    }

    link.setAttribute("download", filename);
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);

    return { success: true, filename };
  } catch (error) {
    if (error.response) {
      const message = error.response.data?.detail || "Download failed";
      throw new Error(message);
    }
    throw new Error("Download failed. Please try again.");
  }
};

export const getProjects = async () => {
  try {
    const response = await api.get("/api/projects");
    return response;
  } catch (error) {
    throw error;
  }
};

export const deleteProject = async (projectId) => {
  try {
    const response = await api.delete(`/api/projects/${projectId}`);
    return response;
  } catch (error) {
    throw error;
  }
};

// Health check
export const healthCheck = async () => {
  try {
    const response = await api.get("/health");
    return response;
  } catch (error) {
    throw error;
  }
};

// Test connection with detailed diagnostics
export const testConnection = async () => {
  console.log("Testing connection to:", api.defaults.baseURL);

  try {
    const response = await api.get("/");
    console.log("Connection test successful:", response);
    return { success: true, data: response };
  } catch (error) {
    console.error("Connection test failed:", error);
    return {
      success: false,
      error: error.message,
      details: {
        baseURL: api.defaults.baseURL,
        timeout: api.defaults.timeout,
        code: error.code,
      },
    };
  }
};

// Test connection with simple fetch (bypass axios)
export const testConnectionDirect = async () => {
  const baseURL = import.meta.env.VITE_API_URL || "http://localhost:8000";
  console.log("Testing direct connection to:", baseURL);

  try {
    const response = await fetch(`${baseURL}/health`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    console.log("Direct connection test successful:", data);
    return { success: true, data };
  } catch (error) {
    console.error("Direct connection test failed:", error);
    return {
      success: false,
      error: error.message,
      details: {
        baseURL,
        type: error.name,
      },
    };
  }
};

export default api;
