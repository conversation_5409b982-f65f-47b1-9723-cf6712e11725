import React, { useState, useEffect } from "react";
import {
  CheckCir<PERSON>,
  Clock,
  Download,
  AlertCircle,
  Loader2,
  RefreshCw,
  Brain,
  Code,
  Lightbulb,
  Settings,
  Edit,
  Plus,
  Eye,
} from "lucide-react";
import {
  getEnhancedProjectStatus,
  getProjectAnalysis,
  getModificationSuggestions,
  requestModifications,
  applyModifications,
  downloadProject,
} from "../services/api";

const IntelligentProjectStatus = ({ projectId, onCompleted }) => {
  const [status, setStatus] = useState(null);
  const [analysis, setAnalysis] = useState(null);
  const [suggestions, setSuggestions] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState("");
  const [isDownloading, setIsDownloading] = useState(false);
  const [activeTab, setActiveTab] = useState("status");
  const [modificationPrompt, setModificationPrompt] = useState("");
  const [isRequestingMods, setIsRequestingMods] = useState(false);
  const [selectedSuggestions, setSelectedSuggestions] = useState(new Set());

  useEffect(() => {
    if (!projectId) return;

    const fetchData = async () => {
      try {
        // Fetch enhanced status
        const statusData = await getEnhancedProjectStatus(projectId);
        setStatus(statusData);

        // Fetch analysis if available
        if (statusData.analysis) {
          setAnalysis(statusData.analysis);
        } else {
          try {
            const analysisData = await getProjectAnalysis(projectId);
            setAnalysis(analysisData);
          } catch (e) {
            // Analysis might not be available yet
          }
        }

        // Fetch suggestions if project is completed
        if (statusData.status === "completed") {
          try {
            const suggestionsData = await getModificationSuggestions(projectId);
            setSuggestions(suggestionsData.suggestions || []);
          } catch (e) {
            // Suggestions might not be available
          }
        }

        setError("");

        // If completed, notify parent
        if (statusData.status === "completed" && onCompleted) {
          setTimeout(() => onCompleted(), 2000);
        }
      } catch (err) {
        setError(err.message || "Failed to fetch status");
      } finally {
        setIsLoading(false);
      }
    };

    // Initial fetch
    fetchData();

    // Poll for updates if not completed
    const interval = setInterval(() => {
      if (status?.status !== "completed" && status?.status !== "failed") {
        fetchData();
      }
    }, 2000);

    return () => clearInterval(interval);
  }, [projectId, status?.status, onCompleted]);

  const handleDownload = async () => {
    if (!projectId) return;

    setIsDownloading(true);
    try {
      await downloadProject(projectId);
    } catch (err) {
      setError(err.message || "Failed to download project");
    } finally {
      setIsDownloading(false);
    }
  };

  const handleRequestModifications = async () => {
    if (!modificationPrompt.trim()) return;

    setIsRequestingMods(true);
    try {
      // Use the same AI provider that was used for the original project generation
      const aiProvider = status?.ai_provider || "openai";
      console.log(`Using AI provider for modifications: ${aiProvider}`);

      const response = await requestModifications(projectId, {
        modification_prompt: modificationPrompt,
        ai_provider: aiProvider,
        apply_immediately: false,
      });

      setSuggestions((prev) => [...prev, ...response.suggestions]);
      setModificationPrompt("");
      setError("");
    } catch (err) {
      setError(err.message || "Failed to request modifications");
    } finally {
      setIsRequestingMods(false);
    }
  };

  const handleApplyModifications = async () => {
    if (selectedSuggestions.size === 0) return;

    try {
      const response = await applyModifications(projectId, {
        modification_ids: Array.from(selectedSuggestions),
      });

      if (response.success) {
        // Refresh status and suggestions
        const statusData = await getEnhancedProjectStatus(projectId);
        setStatus(statusData);

        const suggestionsData = await getModificationSuggestions(projectId);
        setSuggestions(suggestionsData.suggestions || []);

        setSelectedSuggestions(new Set());
        setError("");
      } else {
        setError(response.message || "Failed to apply modifications");
      }
    } catch (err) {
      setError(err.message || "Failed to apply modifications");
    }
  };

  const getStatusIcon = () => {
    if (!status)
      return <Loader2 className="w-6 h-6 animate-spin text-blue-500" />;

    switch (status.status) {
      case "analyzing":
        return <Brain className="w-6 h-6 text-purple-500" />;
      case "generating":
        return <Loader2 className="w-6 h-6 animate-spin text-blue-500" />;
      case "reviewing":
        return <Eye className="w-6 h-6 text-yellow-500" />;
      case "completed":
        return <CheckCircle className="w-6 h-6 text-green-500" />;
      case "failed":
        return <AlertCircle className="w-6 h-6 text-red-500" />;
      default:
        return <Clock className="w-6 h-6 text-gray-500" />;
    }
  };

  const getPhaseColor = (phase) => {
    switch (phase) {
      case "analysis":
        return "bg-purple-100 text-purple-800";
      case "generation":
        return "bg-blue-100 text-blue-800";
      case "review":
        return "bg-yellow-100 text-yellow-800";
      case "modification":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  if (isLoading) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="bg-white shadow-lg rounded-lg p-6">
          <div className="flex items-center justify-center">
            <Loader2 className="w-8 h-8 animate-spin text-purple-500 mr-3" />
            <span className="text-lg text-gray-600">
              Loading intelligent project status...
            </span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white shadow-lg rounded-lg overflow-hidden">
        {/* Header */}
        <div className="px-6 py-4 bg-gradient-to-r from-purple-600 to-blue-600">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              {getStatusIcon()}
              <div className="ml-3">
                <h2 className="text-xl font-bold text-white">
                  Intelligent Project Status
                </h2>
                <p className="text-purple-100">Project ID: {projectId}</p>
              </div>
            </div>
            {status && (
              <div
                className={`px-3 py-1 rounded-full text-sm font-medium ${getPhaseColor(
                  status.phase
                )}`}
              >
                {status.phase}
              </div>
            )}
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            {["status", "analysis", "modifications"].map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`py-2 px-1 border-b-2 font-medium text-sm capitalize ${
                  activeTab === tab
                    ? "border-purple-500 text-purple-600"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                }`}
              >
                {tab}
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
              <div className="flex">
                <AlertCircle className="w-5 h-5 text-red-400 mr-2" />
                <p className="text-red-800">{error}</p>
              </div>
            </div>
          )}

          {/* Status Tab */}
          {activeTab === "status" && status && (
            <div className="space-y-6">
              {/* Progress Bar */}
              <div>
                <div className="flex justify-between text-sm text-gray-600 mb-2">
                  <span>Progress</span>
                  <span>{status.progress}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="h-2 rounded-full transition-all duration-300 bg-gradient-to-r from-purple-500 to-blue-500"
                    style={{ width: `${status.progress}%` }}
                  />
                </div>
              </div>

              {/* Current Step */}
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">
                  Current Step
                </h3>
                <p className="text-gray-900">{status.current_step}</p>
              </div>

              {/* Status Details */}
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-700">Status</h4>
                  <p className="text-lg font-semibold capitalize text-gray-900">
                    {status.status}
                  </p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-700">
                    AI Provider
                  </h4>
                  <p className="text-lg font-semibold text-gray-900 capitalize">
                    {status.ai_provider || "OpenAI"}
                  </p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-700">
                    Can Modify
                  </h4>
                  <p className="text-lg font-semibold text-gray-900">
                    {status.can_modify ? "Yes" : "No"}
                  </p>
                </div>
              </div>

              {/* Actions */}
              {status.status === "completed" && (
                <div className="flex space-x-4 pt-4">
                  <button
                    onClick={handleDownload}
                    disabled={isDownloading}
                    className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50"
                  >
                    {isDownloading ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Downloading...
                      </>
                    ) : (
                      <>
                        <Download className="w-4 h-4 mr-2" />
                        Download Project
                      </>
                    )}
                  </button>
                </div>
              )}
            </div>
          )}

          {/* Analysis Tab */}
          {activeTab === "analysis" && analysis && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-3">
                    Project Overview
                  </h3>
                  <div className="space-y-2">
                    <p>
                      <span className="font-medium">Type:</span>{" "}
                      {analysis.project_type}
                    </p>
                    <p>
                      <span className="font-medium">Name:</span>{" "}
                      {analysis.suggested_name}
                    </p>
                    <p>
                      <span className="font-medium">Architecture:</span>{" "}
                      {analysis.architecture_pattern}
                    </p>
                    <p>
                      <span className="font-medium">Complexity:</span>{" "}
                      {analysis.estimated_complexity}
                    </p>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-3">
                    Technology Stack
                  </h3>
                  <div className="space-y-2">
                    {analysis.technology_stack.frontend && (
                      <p>
                        <span className="font-medium">Frontend:</span>{" "}
                        {analysis.technology_stack.frontend.join(", ")}
                      </p>
                    )}
                    {analysis.technology_stack.backend && (
                      <p>
                        <span className="font-medium">Backend:</span>{" "}
                        {analysis.technology_stack.backend.join(", ")}
                      </p>
                    )}
                    {analysis.technology_stack.database && (
                      <p>
                        <span className="font-medium">Database:</span>{" "}
                        {analysis.technology_stack.database.join(", ")}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-3">
                  Key Features
                </h3>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {analysis.key_features.map((feature, index) => (
                    <span
                      key={index}
                      className="px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm"
                    >
                      {feature}
                    </span>
                  ))}
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-3">
                  AI Reasoning
                </h3>
                <p className="text-gray-700 bg-gray-50 p-4 rounded-lg">
                  {analysis.reasoning}
                </p>
              </div>
            </div>
          )}

          {/* Modifications Tab */}
          {activeTab === "modifications" && (
            <div className="space-y-6">
              {/* Request New Modifications */}
              <div className="border border-gray-200 rounded-lg p-4">
                <h3 className="text-lg font-medium text-gray-900 mb-3 flex items-center">
                  <Plus className="w-5 h-5 mr-2" />
                  Request Modifications
                </h3>
                {status?.ai_provider && (
                  <div className="mb-3 p-2 bg-blue-50 border border-blue-200 rounded-md">
                    <p className="text-sm text-blue-800">
                      <span className="font-medium">AI Provider:</span> Using{" "}
                      {status.ai_provider.toUpperCase()} (same as original
                      project generation)
                    </p>
                  </div>
                )}
                <div className="space-y-3">
                  <textarea
                    value={modificationPrompt}
                    onChange={(e) => setModificationPrompt(e.target.value)}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                    placeholder="Describe what you'd like to modify or add to the project..."
                  />
                  <button
                    onClick={handleRequestModifications}
                    disabled={isRequestingMods || !modificationPrompt.trim()}
                    className="flex items-center px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 disabled:opacity-50"
                  >
                    {isRequestingMods ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Analyzing...
                      </>
                    ) : (
                      <>
                        <Lightbulb className="w-4 h-4 mr-2" />
                        Get AI Suggestions
                      </>
                    )}
                  </button>
                </div>
              </div>

              {/* Suggestions List */}
              {suggestions.length > 0 && (
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-gray-900">
                      AI Suggestions
                    </h3>
                    {selectedSuggestions.size > 0 && (
                      <button
                        onClick={handleApplyModifications}
                        className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
                      >
                        <CheckCircle className="w-4 h-4 mr-2" />
                        Apply Selected ({selectedSuggestions.size})
                      </button>
                    )}
                  </div>

                  <div className="space-y-4">
                    {suggestions.map((suggestion) => (
                      <div
                        key={suggestion.suggestion_id}
                        className="border border-gray-200 rounded-lg p-4"
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex items-start">
                            <input
                              type="checkbox"
                              checked={selectedSuggestions.has(
                                suggestion.suggestion_id
                              )}
                              onChange={(e) => {
                                const newSelected = new Set(
                                  selectedSuggestions
                                );
                                if (e.target.checked) {
                                  newSelected.add(suggestion.suggestion_id);
                                } else {
                                  newSelected.delete(suggestion.suggestion_id);
                                }
                                setSelectedSuggestions(newSelected);
                              }}
                              className="mt-1 rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                            />
                            <div className="ml-3">
                              <h4 className="font-medium text-gray-900">
                                {suggestion.title}
                              </h4>
                              <p className="text-gray-600 mt-1">
                                {suggestion.description}
                              </p>
                              <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                                <span className="px-2 py-1 bg-gray-100 rounded">
                                  {suggestion.category}
                                </span>
                                <span>
                                  Effort: {suggestion.estimated_effort}
                                </span>
                                <span>Impact: {suggestion.impact}</span>
                              </div>
                            </div>
                          </div>
                        </div>

                        {suggestion.files.length > 0 && (
                          <div className="mt-3 pl-6">
                            <p className="text-sm font-medium text-gray-700">
                              Files to modify:
                            </p>
                            <ul className="text-sm text-gray-600 mt-1">
                              {suggestion.files.map((file, index) => (
                                <li key={index} className="flex items-center">
                                  <Code className="w-3 h-3 mr-1" />
                                  {file.file_path} ({file.action})
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default IntelligentProjectStatus;
