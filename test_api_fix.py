#!/usr/bin/env python3
"""
Test the API fix by making a direct request
"""

import requests
import json

def test_api_analysis():
    """Test the analysis endpoint with a request that might trigger the nested dependency issue"""
    
    url = "http://localhost:8000/api/intelligent/analyze"
    
    payload = {
        "prompt": "Create a dashboard application with real-time data visualization, user authentication, and data export features",
        "ai_provider": "openai",
        "complexity_level": "medium",
        "include_tests": True,
        "include_documentation": True
    }
    
    try:
        print("Making request to analysis endpoint...")
        response = requests.post(url, json=payload, timeout=30)
        
        print(f"Status code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ SUCCESS: Analysis completed without validation errors")
            
            if "analysis" in data:
                analysis = data["analysis"]
                print(f"Project name: {analysis.get('suggested_name', 'N/A')}")
                print(f"Project type: {analysis.get('project_type', 'N/A')}")
                print(f"Dependencies: {analysis.get('dependencies', {})}")
                
                # Check if dependencies are properly formatted
                dependencies = analysis.get('dependencies', {})
                for key, value in dependencies.items():
                    if not isinstance(value, list):
                        print(f"❌ WARNING: {key} is not a list: {type(value)}")
                    elif not all(isinstance(item, str) for item in value):
                        print(f"❌ WARNING: {key} contains non-string items: {value}")
                    else:
                        print(f"✅ {key}: {value}")
            
            return True
        else:
            print(f"❌ ERROR: Request failed with status {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ ERROR: Request failed: {e}")
        return False
    except Exception as e:
        print(f"❌ ERROR: Unexpected error: {e}")
        return False

if __name__ == "__main__":
    success = test_api_analysis()
    if success:
        print("\n🎉 API test completed successfully!")
    else:
        print("\n💥 API test failed!")
