import os
import uuid
import json
import re
from typing import Dict, Any, List
import requests

# Use compatible imports for <PERSON><PERSON><PERSON><PERSON>
try:
    from langchain_openai import Chat<PERSON>penA<PERSON>
    from langchain_core.messages import HumanMessage, SystemMessage

    LANGCHAIN_AVAILABLE = True
except ImportError:
    try:
        from langchain.chat_models import ChatOpenA<PERSON>
        from langchain.schema import HumanMessage, SystemMessage

        LANGCHAIN_AVAILABLE = True
    except ImportError:
        # If <PERSON><PERSON><PERSON><PERSON> is not available, we'll use direct OpenAI API
        ChatOpenAI = None
        HumanMessage = None
        SystemMessage = None
        LANGCHAIN_AVAILABLE = False

from app.models.project_models import (
    AIProvider,
    IntelligentProjectRequest,
    ProjectAnalysis,
    TechnologyStack,
    ModificationSuggestion,
    FileModification,
)


class IntelligentAIService:
    """Enhanced AI service with intelligent prompt understanding and modification capabilities"""

    def __init__(self):
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        self.huggingface_api_key = os.getenv("HUGGINGFACE_API_KEY")
        self.grok_api_key = os.getenv("GROK_API_KEY")

    async def analyze_project_requirements(
        self, request: IntelligentProjectRequest
    ) -> ProjectAnalysis:
        """Analyze project requirements from natural language prompt"""

        print(f"DEBUG: Creating analysis prompt for {request.ai_provider}")
        analysis_prompt = self._create_analysis_prompt(request)
        print(f"DEBUG: Analysis prompt created, length: {len(analysis_prompt)}")

        if request.ai_provider == AIProvider.OPENAI:
            print("DEBUG: Using OpenAI for analysis")
            response = await self._analyze_with_openai(analysis_prompt)
        elif request.ai_provider == AIProvider.HUGGINGFACE:
            print("DEBUG: Using Hugging Face for analysis")
            response = await self._analyze_with_huggingface(analysis_prompt)
        elif request.ai_provider == AIProvider.GROK:
            print("DEBUG: Using Grok for analysis")
            response = await self._analyze_with_grok(analysis_prompt)
        else:
            raise ValueError(f"Unsupported AI provider: {request.ai_provider}")

        print(f"DEBUG: AI response received, length: {len(response)}")
        analysis = self._parse_analysis_response(response, request)
        print(f"DEBUG: Analysis parsed successfully: {analysis.suggested_name}")
        return analysis

    async def generate_intelligent_project(
        self, analysis: ProjectAnalysis, request: IntelligentProjectRequest
    ) -> Dict[str, Any]:
        """Generate project based on AI analysis"""

        generation_prompt = self._create_generation_prompt(analysis, request)

        if request.ai_provider == AIProvider.OPENAI:
            response = await self._analyze_with_openai(generation_prompt)
        elif request.ai_provider == AIProvider.HUGGINGFACE:
            response = await self._analyze_with_huggingface(generation_prompt)
        elif request.ai_provider == AIProvider.GROK:
            response = await self._analyze_with_grok(generation_prompt)
        else:
            raise ValueError(f"Unsupported AI provider: {request.ai_provider}")

        return self._parse_generation_response(response)

    async def suggest_modifications(
        self,
        project_id: str,
        modification_prompt: str,
        current_files: Dict[str, str],
        ai_provider: AIProvider = AIProvider.OPENAI,
    ) -> List[ModificationSuggestion]:
        """Generate modification suggestions based on user prompt"""

        modification_analysis_prompt = self._create_modification_prompt(
            modification_prompt, current_files
        )

        if ai_provider == AIProvider.OPENAI:
            response = await self._analyze_with_openai(modification_analysis_prompt)
        elif ai_provider == AIProvider.HUGGINGFACE:
            response = await self._analyze_with_huggingface(
                modification_analysis_prompt
            )
        elif ai_provider == AIProvider.GROK:
            response = await self._analyze_with_grok(modification_analysis_prompt)
        else:
            response = await self._analyze_with_openai(modification_analysis_prompt)

        return self._parse_modification_response(response, project_id)

    def _create_analysis_prompt(self, request: IntelligentProjectRequest) -> str:
        """Create prompt for project requirement analysis"""

        return f"""
You are an expert software architect and technology consultant. Analyze the following project requirements and provide a comprehensive technical analysis.

PROJECT PROMPT: {request.prompt}

USER PREFERENCES:
- Preferred Technologies: {request.preferred_technologies or 'None specified'}
- Target Platforms: {request.target_platform or 'Not specified'}
- Complexity Level: {request.complexity_level}
- Constraints: {request.constraints or 'None specified'}
- Include Tests: {request.include_tests}
- Include Documentation: {request.include_documentation}
- Include Deployment: {request.include_deployment}
- Include CI/CD: {request.include_ci_cd}

ANALYSIS GUIDELINES:
1. **Project Type Detection**:
   - If the prompt mentions "web app", "dashboard", "website", "UI", "frontend", or user interface → likely needs frontend
   - If the prompt mentions "API", "backend", "server", "database", "microservice" → likely needs backend
   - If the prompt mentions "full-stack", "complete app", or both UI and API features → fullstack
   - Default to fullstack for comprehensive applications

2. **Technology Stack Selection**:
   - Frontend: Prefer React + TypeScript + Vite for modern web apps
   - Backend: Prefer FastAPI + Python for APIs, Express.js for Node.js projects
   - Database: SQLite for simple projects, PostgreSQL for complex ones
   - Styling: Tailwind CSS for modern, responsive design

3. **Feature Analysis**: Extract specific features mentioned in the prompt and suggest additional complementary features

Please provide a detailed analysis in the following JSON format:
{{
    "project_type": "fullstack|frontend|backend|mobile|desktop|api|microservice",
    "suggested_name": "project-name-suggestion",
    "technology_stack": {{
        "frontend": ["React", "TypeScript", "Vite"],
        "backend": ["FastAPI", "Python"],
        "database": ["SQLite"],
        "deployment": ["Vercel", "Railway"],
        "testing": ["Vitest", "Pytest"],
        "styling": ["Tailwind CSS"],
        "state_management": ["React Context"],
        "authentication": ["JWT"],
        "additional_tools": ["Axios", "React Router"]
    }},
    "architecture_pattern": "MVC|microservices|serverless|monolithic|jamstack",
    "estimated_complexity": "simple|medium|complex|enterprise",
    "key_features": ["feature1", "feature2", "feature3"],
    "technical_requirements": ["requirement1", "requirement2"],
    "suggested_folder_structure": {{
        "frontend": {{"src": {{"components": {{}}, "pages": {{}}, "services": {{}}}},
        "backend": {{"src": {{"controllers": {{}}, "models": {{}}, "services": {{}}}},
        "shared": {{"types": {{}}, "utils": {{}}}}
    }},
    "dependencies": {{
        "frontend": ["react", "typescript", "vite", "tailwindcss", "axios", "react-router-dom"],
        "backend": ["fastapi", "uvicorn", "sqlalchemy", "pydantic"],
        "devDependencies": ["vitest", "eslint", "prettier"],
        "testing": ["vitest", "pytest", "jest"],
        "database": ["sqlalchemy", "sqlite3"],
        "deployment": ["vercel", "railway"]
    }},
    "reasoning": "Detailed explanation of technology choices and architecture decisions"
}}

CRITICAL REQUIREMENTS:
1. Always include both frontend AND backend for fullstack projects
2. Ensure technology choices match the project requirements
3. Consider the user's preferred technologies when specified
4. Provide realistic complexity assessment
5. Include comprehensive feature list based on the prompt
6. Explain your reasoning clearly

Focus on:
1. Understanding the core business requirements from the prompt
2. Detecting whether frontend, backend, or both are needed
3. Selecting the most appropriate technology stack
4. Considering scalability and maintainability
5. Balancing complexity with user preferences
6. Providing clear reasoning for all decisions
"""

    def _create_generation_prompt(
        self, analysis: ProjectAnalysis, request: IntelligentProjectRequest
    ) -> str:
        """Create prompt for project code generation"""

        tech_stack = analysis.technology_stack
        project_type = analysis.project_type

        # Determine what needs to be generated
        needs_frontend = (
            project_type in ["fullstack", "frontend"] or tech_stack.frontend
        )
        needs_backend = (
            project_type in ["fullstack", "backend", "api"] or tech_stack.backend
        )

        return f"""
You are an expert full-stack developer. Generate a complete, production-ready project based on the following analysis.

PROJECT ANALYSIS:
- Project Type: {project_type}
- Project Name: {analysis.suggested_name}
- Technology Stack: {tech_stack.model_dump_json()}
- Architecture: {analysis.architecture_pattern}
- Key Features: {', '.join(analysis.key_features)}
- Needs Frontend: {needs_frontend}
- Needs Backend: {needs_backend}

ORIGINAL REQUEST: {request.prompt}

CRITICAL GENERATION REQUIREMENTS:
1. **Frontend Generation** (Required: {needs_frontend}):
   {f"- Generate React + TypeScript frontend in 'frontend/' directory" if needs_frontend else "- No frontend needed"}
   {f"- Include components, pages, routing, and styling" if needs_frontend else ""}
   {f"- Use {', '.join(tech_stack.frontend)} technologies" if needs_frontend and tech_stack.frontend else ""}

2. **Backend Generation** (Required: {needs_backend}):
   {f"- Generate FastAPI backend in 'backend/' directory" if needs_backend else "- No backend needed"}
   {f"- Include API endpoints, models, and database setup" if needs_backend else ""}
   {f"- Use {', '.join(tech_stack.backend)} technologies" if needs_backend and tech_stack.backend else ""}

3. **Project Structure**:
   - Root README.md with setup instructions
   - Separate frontend/ and backend/ directories if both are needed
   - Configuration files for each part
   - Environment setup files

CRITICAL: You MUST return a valid JSON object with the exact structure below. Do not include any text before or after the JSON.

Generate a complete project with ALL necessary files. Include:
1. Configuration files (package.json, requirements.txt, etc.)
2. Main application files with complete implementation
3. Database models and schemas (if backend needed)
4. API endpoints and routes (if backend needed)
5. Frontend components and pages (if frontend needed)
6. Styling files and UI components (if frontend needed)
7. Test files and configurations
8. Environment configuration files
9. Documentation (README.md)
10. Deployment configurations

Return ONLY this JSON structure:
{{
    "files": {{
        "README.md": "# {analysis.suggested_name}\\n\\nComplete project documentation...",
        {"frontend/package.json" if needs_frontend else "package.json"}: "Frontend package.json with dependencies...",
        {"backend/requirements.txt" if needs_backend else "requirements.txt"}: "Backend requirements...",
        {"frontend/src/App.tsx" if needs_frontend else "src/App.js"}: "Main App component...",
        {"backend/main.py" if needs_backend else "main.py"}: "FastAPI main application..."
    }},
    "instructions": "Setup instructions for the generated project",
    "next_steps": ["Install dependencies", "Configure environment", "Start servers"],
    "additional_notes": "Project generated with {project_type} architecture"
}}

REQUIREMENTS:
- Generate COMPLETE, working code (not placeholders)
- Include ALL necessary files for a working project
- Follow best practices for the chosen technology stack
- Add proper error handling and validation
- Include comprehensive documentation
- Ensure code is production-ready
- Generate at least 15-20 files for fullstack projects
- Include proper configuration files for each technology
- Create proper folder structure (frontend/, backend/ if both needed)

RESPOND WITH ONLY THE JSON OBJECT - NO OTHER TEXT!
"""

    def _create_modification_prompt(
        self, modification_prompt: str, current_files: Dict[str, str]
    ) -> str:
        """Create prompt for project modifications"""

        files_summary = self._summarize_current_files(current_files)

        return f"""
You are an expert software developer. Analyze the following modification request for an existing project and suggest specific changes.

MODIFICATION REQUEST: {modification_prompt}

CURRENT PROJECT STRUCTURE:
{files_summary}

Analyze the request and provide modification suggestions in the following JSON format:
{{
    "suggestions": [
        {{
            "suggestion_id": "unique-id",
            "title": "Brief title of the modification",
            "description": "Detailed description of what will be changed",
            "category": "feature|bugfix|optimization|refactor",
            "files": [
                {{
                    "file_path": "path/to/file.ext",
                    "action": "create|modify|delete",
                    "content": "complete new/modified file content",
                    "reason": "why this change is needed",
                    "priority": "low|medium|high|critical"
                }}
            ],
            "estimated_effort": "small|medium|large",
            "impact": "low|medium|high",
            "reasoning": "detailed explanation of the changes"
        }}
    ]
}}

Focus on:
1. Understanding the exact requirements
2. Minimizing breaking changes
3. Following existing code patterns
4. Maintaining code quality
5. Providing complete, working implementations
"""

    def _summarize_current_files(self, files: Dict[str, str]) -> str:
        """Create a summary of current project files"""
        summary = []
        for file_path, content in files.items():
            lines = len(content.split("\n"))
            summary.append(f"- {file_path} ({lines} lines)")
        return "\n".join(summary[:20])  # Limit to first 20 files

    async def _analyze_with_openai(self, prompt: str) -> str:
        """Analyze using OpenAI"""
        try:
            if LANGCHAIN_AVAILABLE and ChatOpenAI and HumanMessage and SystemMessage:
                # Use LangChain if available
                chat = ChatOpenAI(
                    openai_api_key=self.openai_api_key,
                    model_name="gpt-4-1106-preview",
                    temperature=0.1,
                    max_tokens=4000,
                )

                messages = [
                    SystemMessage(
                        content="You are an expert software architect and full-stack developer. You generate complete, production-ready code and project structures. Always return valid JSON when requested."
                    ),
                    HumanMessage(content=prompt),
                ]

                response = await chat.agenerate([messages])
                return response.generations[0][0].text
            else:
                # Use direct OpenAI API
                import openai

                openai.api_key = self.openai_api_key

                response = await openai.ChatCompletion.acreate(
                    model="gpt-4-1106-preview",
                    messages=[
                        {
                            "role": "system",
                            "content": "You are an expert software architect and full-stack developer. You generate complete, production-ready code and project structures. Always return valid JSON when requested.",
                        },
                        {"role": "user", "content": prompt},
                    ],
                    temperature=0.1,
                    max_tokens=4000,
                )
                return response.choices[0].message.content
        except Exception as e:
            raise Exception(f"OpenAI analysis failed: {str(e)}")

    async def _analyze_with_huggingface(self, prompt: str) -> str:
        """Analyze using Hugging Face"""
        try:
            # Using Hugging Face Inference API with code-generation models
            headers = {"Authorization": f"Bearer {self.huggingface_api_key}"}

            # Try multiple models for better code generation
            models_to_try = [
                "microsoft/CodeGPT-small-py",  # Code generation model
                "microsoft/DialoGPT-large",  # Conversational model
                "bigcode/starcoder",  # Code-specific model
            ]

            for model in models_to_try:
                try:
                    api_url = f"https://api-inference.huggingface.co/models/{model}"
                    payload = {
                        "inputs": prompt,
                        "parameters": {
                            "max_length": 2000,
                            "temperature": 0.3,
                            "do_sample": True,
                        },
                    }

                    response = requests.post(
                        api_url, headers=headers, json=payload, timeout=30
                    )

                    if response.status_code == 200:
                        result = response.json()
                        if isinstance(result, list) and len(result) > 0:
                            generated_text = result[0].get("generated_text", "")
                            if generated_text and len(generated_text) > len(prompt):
                                return generated_text
                        elif isinstance(result, dict) and "generated_text" in result:
                            return result["generated_text"]
                except Exception:
                    continue  # Try next model

            # If all Hugging Face models fail, raise an error
            raise Exception("All Hugging Face models failed to respond")

        except Exception as e:
            # Re-raise the exception instead of falling back
            raise Exception(f"Hugging Face analysis failed: {str(e)}")

    async def _analyze_with_grok(self, prompt: str) -> str:
        """Analyze using Grok"""
        try:
            # Using Grok API (X.AI)
            headers = {
                "Authorization": f"Bearer {self.grok_api_key}",
                "Content-Type": "application/json",
            }

            # Grok API endpoint (this is a placeholder - update with actual endpoint when available)
            api_url = "https://api.x.ai/v1/chat/completions"

            payload = {
                "messages": [
                    {
                        "role": "system",
                        "content": "You are an expert software architect and developer.",
                    },
                    {"role": "user", "content": prompt},
                ],
                "model": "grok-beta",
                "temperature": 0.3,
                "max_tokens": 4000,
            }

            response = requests.post(api_url, headers=headers, json=payload, timeout=30)

            if response.status_code == 200:
                result = response.json()
                if "choices" in result and len(result["choices"]) > 0:
                    return result["choices"][0]["message"]["content"]
                else:
                    # Raise error if Grok fails
                    raise Exception("Grok API returned invalid response format")
            else:
                # Raise error if Grok fails
                raise Exception(
                    f"Grok API request failed with status {response.status_code}"
                )

        except Exception as e:
            # Re-raise the exception instead of falling back
            raise Exception(f"Grok analysis failed: {str(e)}")

    def _parse_analysis_response(
        self, response: str, request: IntelligentProjectRequest
    ) -> ProjectAnalysis:
        """Parse AI analysis response into ProjectAnalysis model"""
        try:
            # Extract JSON from response
            json_match = re.search(r"\{.*\}", response, re.DOTALL)
            if json_match:
                analysis_data = json.loads(json_match.group())

                # Create TechnologyStack
                tech_stack_data = analysis_data.get("technology_stack", {})
                technology_stack = TechnologyStack(**tech_stack_data)

                # Process dependencies to handle nested structures
                dependencies = self._flatten_dependencies(
                    analysis_data.get("dependencies", {})
                )

                # Create ProjectAnalysis
                return ProjectAnalysis(
                    project_type=analysis_data.get("project_type", "fullstack"),
                    suggested_name=analysis_data.get(
                        "suggested_name", request.project_name or "ai-generated-project"
                    ),
                    technology_stack=technology_stack,
                    architecture_pattern=analysis_data.get(
                        "architecture_pattern", "MVC"
                    ),
                    estimated_complexity=analysis_data.get(
                        "estimated_complexity", request.complexity_level
                    ),
                    key_features=analysis_data.get("key_features", []),
                    technical_requirements=analysis_data.get(
                        "technical_requirements", []
                    ),
                    suggested_folder_structure=analysis_data.get(
                        "suggested_folder_structure", {}
                    ),
                    dependencies=dependencies,
                    reasoning=analysis_data.get("reasoning", "AI analysis completed"),
                )
            else:
                raise ValueError("No valid JSON found in response")

        except Exception as e:
            # Re-raise the exception instead of using fallback
            raise Exception(f"Failed to parse analysis response: {str(e)}")

    def _flatten_dependencies(
        self, dependencies: Dict[str, Any]
    ) -> Dict[str, List[str]]:
        """Flatten nested dependency structures to match expected format"""
        flattened = {}

        for key, value in dependencies.items():
            if isinstance(value, list):
                # Already in correct format
                flattened[key] = value
            elif isinstance(value, dict):
                # Nested structure - flatten it
                for sub_key, sub_value in value.items():
                    if isinstance(sub_value, list):
                        # Create a new key combining the parent and child keys
                        new_key = (
                            f"{key}_{sub_key}"
                            if key != "testing"
                            else f"{sub_key}_testing"
                        )
                        flattened[new_key] = sub_value
                    else:
                        # Convert single values to list
                        new_key = (
                            f"{key}_{sub_key}"
                            if key != "testing"
                            else f"{sub_key}_testing"
                        )
                        flattened[new_key] = [str(sub_value)]
            else:
                # Single value - convert to list
                flattened[key] = [str(value)]

        return flattened

    def _parse_generation_response(self, response: str) -> Dict[str, Any]:
        """Parse AI generation response"""
        print(f"DEBUG: Parsing generation response, length: {len(response)}")
        print(f"DEBUG: Response contains 'files': {'files' in response}")
        print(f"DEBUG: Response contains 'instructions': {response}")

        try:
            # Try to find JSON in the response
            json_match = re.search(r"\{.*\}", response, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                print(f"DEBUG: Found JSON, length: {len(json_str)}")
                parsed_data = json.loads(json_str)

                # Validate that we have files
                if "files" in parsed_data and isinstance(parsed_data["files"], dict):
                    file_count = len(parsed_data["files"])
                    print(f"DEBUG: Successfully parsed {file_count} files")

                    # Validate minimum file count
                    if file_count < 2:
                        raise Exception(
                            f"Insufficient files generated: only {file_count} files"
                        )

                    return parsed_data
                else:
                    raise Exception("No valid files structure found in JSON response")
            else:
                raise Exception("No JSON found in AI response")

        except json.JSONDecodeError as e:
            raise Exception(f"JSON parsing failed: {e}")
        except Exception as e:
            raise Exception(f"Unexpected error in parsing generation response: {e}")

    def _parse_modification_response(
        self, response: str, project_id: str = None
    ) -> List[ModificationSuggestion]:
        """Parse AI modification response"""
        try:
            json_match = re.search(r"\{.*\}", response, re.DOTALL)
            if json_match:
                data = json.loads(json_match.group())
                suggestions = []

                for suggestion_data in data.get("suggestions", []):
                    files = []
                    for file_data in suggestion_data.get("files", []):
                        files.append(FileModification(**file_data))

                    suggestion = ModificationSuggestion(
                        suggestion_id=suggestion_data.get(
                            "suggestion_id", str(uuid.uuid4())
                        ),
                        title=suggestion_data.get("title", "Modification"),
                        description=suggestion_data.get("description", ""),
                        category=suggestion_data.get("category", "feature"),
                        files=files,
                        estimated_effort=suggestion_data.get(
                            "estimated_effort", "medium"
                        ),
                        impact=suggestion_data.get("impact", "medium"),
                        reasoning=suggestion_data.get("reasoning", ""),
                    )
                    suggestions.append(suggestion)

                return suggestions
            else:
                return []
        except Exception as e:
            return [
                ModificationSuggestion(
                    suggestion_id=str(uuid.uuid4()),
                    title="Parsing Error",
                    description=f"Failed to parse modification suggestions: {str(e)}",
                    category="bugfix",
                    files=[],
                    estimated_effort="small",
                    impact="low",
                    reasoning="Error in response parsing",
                )
            ]
