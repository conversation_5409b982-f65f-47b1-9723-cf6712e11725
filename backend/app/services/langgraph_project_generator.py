import os
import json
import uuid
import zipfile
import requests
from typing import Dict, Any, List, Optional
from pathlib import Path
from datetime import datetime

from langchain_core.messages import HumanMessage, SystemMessage
from langchain_openai import ChatOpenAI

from app.models.project_models import (
    IntelligentProjectRequest,
    IntelligentProjectResponse,
    ProjectAnalysis,
    TechnologyStack,
    ModificationSuggestion,
    FileModification,
    EnhancedGenerationStatus,
    ModificationRequest,
    ModificationResponse,
    ApplyModificationRequest,
    AIProvider,
)
from app.utils.file_utils import FileUtils


class LangGraphProjectGenerator:
    """Enhanced AI-driven project generator with full project management capabilities"""

    def __init__(self):
        # AI provider API keys
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        self.huggingface_api_key = os.getenv("HUGGINGFACE_API_KEY")
        self.grok_api_key = os.getenv("GROK_API_KEY")

        self.file_utils = FileUtils()

        # Project management state
        self.generation_status: Dict[str, EnhancedGenerationStatus] = {}
        self.project_analyses: Dict[str, ProjectAnalysis] = {}
        self.project_files: Dict[str, Dict[str, str]] = {}  # project_id -> files
        self.modification_suggestions: Dict[str, List[ModificationSuggestion]] = {}

        # Output directory
        self.output_dir = Path("generated_projects")
        self.output_dir.mkdir(exist_ok=True)

        # Note: LLM will be created dynamically based on the request's ai_provider
        print("LangGraphProjectGenerator initialized with multi-provider support")

    def _get_llm_for_provider(self, ai_provider: AIProvider):
        """Create and return the appropriate LLM based on the AI provider"""
        if ai_provider == AIProvider.OPENAI:
            if not self.openai_api_key:
                raise ValueError("OPENAI_API_KEY not set")
            return ChatOpenAI(
                api_key=self.openai_api_key,
                model="gpt-4-1106-preview",
                temperature=0.1,
                max_tokens=4000,
            )
        elif ai_provider == AIProvider.HUGGINGFACE:
            if not self.huggingface_api_key:
                raise ValueError("HUGGINGFACE_API_KEY not set")
            # For HuggingFace, we'll use direct API calls
            return None  # Will be handled in _call_ai_provider
        elif ai_provider == AIProvider.GROK:
            if not self.grok_api_key:
                raise ValueError("GROK_API_KEY not set")
            # For Grok, we'll use direct API calls
            return None  # Will be handled in _call_ai_provider
        else:
            raise ValueError(f"Unsupported AI provider: {ai_provider}")

    async def _call_ai_provider(self, messages: List, ai_provider: AIProvider) -> str:
        """Call the appropriate AI provider with the given messages"""
        if ai_provider == AIProvider.OPENAI:
            llm = self._get_llm_for_provider(ai_provider)
            response = await llm.ainvoke(messages)
            return response.content
        elif ai_provider == AIProvider.HUGGINGFACE:
            return await self._call_huggingface(messages)
        elif ai_provider == AIProvider.GROK:
            return await self._call_grok(messages)
        else:
            raise ValueError(f"Unsupported AI provider: {ai_provider}")

    async def _call_huggingface(self, messages: List) -> str:
        """Call HuggingFace API"""
        try:
            # Convert LangChain messages to HuggingFace format
            prompt = ""
            for msg in messages:
                if hasattr(msg, "content"):
                    prompt += f"{msg.content}\n"
                else:
                    prompt += f"{str(msg)}\n"

            headers = {
                "Authorization": f"Bearer {self.huggingface_api_key}",
                "Content-Type": "application/json",
            }

            # Using a capable model for code generation
            api_url = (
                "https://api-inference.huggingface.co/models/microsoft/DialoGPT-large"
            )

            payload = {
                "inputs": prompt,
                "parameters": {
                    "max_new_tokens": 4000,
                    "temperature": 0.1,
                    "return_full_text": False,
                },
            }

            response = requests.post(api_url, headers=headers, json=payload, timeout=60)

            if response.status_code == 200:
                result = response.json()
                if isinstance(result, list) and len(result) > 0:
                    return result[0].get("generated_text", "")
                elif isinstance(result, dict):
                    return result.get("generated_text", "")
                else:
                    raise Exception("HuggingFace API returned unexpected format")
            else:
                raise Exception(
                    f"HuggingFace API request failed with status {response.status_code}: {response.text}"
                )

        except Exception as e:
            raise Exception(f"HuggingFace analysis failed: {str(e)}")

    async def _call_grok(self, messages: List) -> str:
        """Call Grok API"""
        try:
            headers = {
                "Authorization": f"Bearer {self.grok_api_key}",
                "Content-Type": "application/json",
            }

            # Convert LangChain messages to Grok format
            grok_messages = []
            for msg in messages:
                if hasattr(msg, "type"):
                    if msg.type == "system":
                        grok_messages.append({"role": "system", "content": msg.content})
                    elif msg.type == "human":
                        grok_messages.append({"role": "user", "content": msg.content})
                else:
                    grok_messages.append({"role": "user", "content": str(msg)})

            api_url = "https://api.x.ai/v1/chat/completions"

            payload = {
                "messages": grok_messages,
                "model": "grok-beta",
                "temperature": 0.1,
                "max_tokens": 4000,
            }

            response = requests.post(api_url, headers=headers, json=payload, timeout=60)

            if response.status_code == 200:
                result = response.json()
                if "choices" in result and len(result["choices"]) > 0:
                    return result["choices"][0]["message"]["content"]
                else:
                    raise Exception("Grok API returned invalid response format")
            else:
                raise Exception(
                    f"Grok API request failed with status {response.status_code}: {response.text}"
                )

        except Exception as e:
            raise Exception(f"Grok analysis failed: {str(e)}")

    async def analyze_project_requirements(
        self, request: IntelligentProjectRequest
    ) -> ProjectAnalysis:
        """Analyze project requirements using AI"""

        analysis_prompt = f"""
        You are an expert software architect and full-stack developer with 15+ years of experience.
        Analyze this project request and provide a comprehensive, production-ready technical analysis.

        PROJECT REQUEST: {request.prompt}

        USER PREFERENCES:
        - Preferred Technologies: {request.preferred_technologies or 'None specified'}
        - Target Platforms: {request.target_platform or 'Web application'}
        - Complexity Level: {request.complexity_level}
        - Include Tests: {request.include_tests}
        - Include Documentation: {request.include_documentation}
        - Include Deployment: {request.include_deployment}
        - Include CI/CD: {request.include_ci_cd}

        ANALYSIS GUIDELINES:
        1. **Project Type Detection**:
           - Analyze the prompt for UI/frontend needs (dashboard, website, interface, forms)
           - Analyze for backend needs (API, database, authentication, business logic)
           - Default to "fullstack" for comprehensive applications

        2. **Technology Stack Selection** (prefer modern, production-ready technologies):
           - Frontend: React 18 + TypeScript + Vite for modern web apps
           - Backend: FastAPI + Python for high-performance APIs
           - Database: PostgreSQL for production, SQLite for simple projects
           - Styling: Tailwind CSS for modern, responsive design
           - Testing: Vitest + React Testing Library for frontend, Pytest for backend

        3. **Architecture Patterns**:
           - MVC for traditional applications
           - Microservices for complex, scalable systems
           - JAMstack for static/content sites
           - Serverless for event-driven applications

        4. **Feature Analysis**: Extract ALL features mentioned and suggest complementary ones
        5. **Dependencies**: Provide FLAT structure - no nested objects

        Provide analysis in this EXACT JSON format:
        {{
            "project_type": "fullstack|frontend|backend|mobile|api|microservice",
            "suggested_name": "kebab-case-project-name",
            "technology_stack": {{
                "frontend": ["React", "TypeScript", "Vite"],
                "backend": ["FastAPI", "Python"],
                "database": ["PostgreSQL"],
                "styling": ["Tailwind CSS"],
                "testing": ["Vitest", "Pytest"],
                "state_management": ["React Context"],
                "authentication": ["JWT"],
                "deployment": ["Vercel", "Railway"]
            }},
            "architecture_pattern": "MVC|microservices|serverless|jamstack",
            "estimated_complexity": "simple|medium|complex|enterprise",
            "key_features": ["feature1", "feature2", "feature3"],
            "technical_requirements": ["requirement1", "requirement2"],
            "suggested_folder_structure": {{
                "frontend": {{"src": {{"components": {{}}, "pages": {{}}, "hooks": {{}}}}}},
                "backend": {{"app": {{"api": {{}}, "models": {{}}, "services": {{}}}}}}
            }},
            "dependencies": {{
                "frontend": ["react", "typescript", "vite", "tailwindcss"],
                "backend": ["fastapi", "uvicorn", "sqlalchemy"],
                "testing": ["vitest", "pytest"],
                "devDependencies": ["eslint", "prettier"]
            }},
            "reasoning": "Detailed explanation of technology choices and architecture decisions"
        }}

        CRITICAL: Return ONLY the JSON object. No markdown, no explanations, just valid JSON.
        """

        messages = [
            SystemMessage(
                content="You are an expert software architect providing technical analysis."
            ),
            HumanMessage(content=analysis_prompt),
        ]

        try:
            print(f"DEBUG: Using AI provider: {request.ai_provider}")
            response_content = await self._call_ai_provider(
                messages, request.ai_provider
            )
        except Exception as e:
            print(f"WARNING: AI provider {request.ai_provider} failed: {e}")
            print("Falling back to basic analysis")
            return self._create_basic_analysis(request)

        try:
            # Debug: Print the raw response
            print(f"DEBUG: Raw AI response: {response_content[:500]}...")

            # Try to extract JSON from the response
            response_text = response_content.strip()

            # Look for JSON content in the response
            json_start = response_text.find("{")
            json_end = response_text.rfind("}") + 1

            if json_start == -1 or json_end == 0:
                # No JSON found, create a basic analysis
                print("WARNING: No JSON found in AI response, creating basic analysis")
                return self._create_basic_analysis(request)

            json_content = response_text[json_start:json_end]
            analysis_data = json.loads(json_content)

            # Create TechnologyStack - handle both string and list formats
            tech_stack_data = analysis_data.get("technology_stack", {})

            # Process and flatten technology stack data
            processed_tech_stack = self._process_technology_stack(tech_stack_data)

            technology_stack = TechnologyStack(**processed_tech_stack)

            # Create ProjectAnalysis
            return ProjectAnalysis(
                project_type=analysis_data.get("project_type", "fullstack"),
                suggested_name=analysis_data.get(
                    "suggested_name", request.project_name or "ai-project"
                ),
                technology_stack=technology_stack,
                architecture_pattern=analysis_data.get("architecture_pattern", "MVC"),
                estimated_complexity=analysis_data.get(
                    "estimated_complexity", request.complexity_level
                ),
                key_features=analysis_data.get("key_features", []),
                technical_requirements=analysis_data.get("technical_requirements", []),
                suggested_folder_structure=analysis_data.get(
                    "suggested_folder_structure", {}
                ),
                dependencies=self._flatten_dependencies(
                    analysis_data.get("dependencies", {})
                ),
                reasoning=str(analysis_data.get("reasoning", "AI analysis completed")),
            )
        except json.JSONDecodeError as e:
            print(f"WARNING: JSON parsing failed: {e}")
            return self._create_basic_analysis(request)
        except Exception as e:
            print(f"ERROR: Analysis failed: {e}")
            return self._create_basic_analysis(request)

    async def generate_intelligent_project(
        self, request: IntelligentProjectRequest
    ) -> IntelligentProjectResponse:
        """Generate a complete project using enhanced LangGraph workflow"""

        project_id = str(uuid.uuid4())

        # Initialize generation status
        self.generation_status[project_id] = EnhancedGenerationStatus(
            project_id=project_id,
            status="analyzing",
            progress=0,
            current_step="Starting project analysis",
            phase="analysis",
            ai_provider=request.ai_provider,  # Store the AI provider used
            can_modify=False,
        )

        try:
            # Step 1: Analyze requirements
            self.generation_status[project_id].current_step = (
                "Analyzing project requirements"
            )
            self.generation_status[project_id].progress = 10

            analysis = await self.analyze_project_requirements(request)
            self.project_analyses[project_id] = analysis
            self.generation_status[project_id].analysis = analysis

            # Step 2: Generate project
            self.generation_status[project_id].status = "generating"
            self.generation_status[project_id].phase = "generation"
            self.generation_status[project_id].current_step = "Generating project files"
            self.generation_status[project_id].progress = 30

            project_data = await self.generate_project(request)

            # Step 3: Save files
            self.generation_status[project_id].current_step = "Saving project files"
            self.generation_status[project_id].progress = 80

            project_files = project_data["files"]
            self.project_files[project_id] = project_files

            # Create project directory and save files
            project_dir = (
                self.output_dir / f"{analysis.suggested_name}_{project_id[:8]}"
            )
            project_dir.mkdir(exist_ok=True)

            for file_path, content in project_files.items():
                full_path = project_dir / file_path
                full_path.parent.mkdir(parents=True, exist_ok=True)
                full_path.write_text(content, encoding="utf-8")

            # Create ZIP file
            zip_path = project_dir.with_suffix(".zip")
            with zipfile.ZipFile(zip_path, "w", zipfile.ZIP_DEFLATED) as zipf:
                for file_path, content in project_files.items():
                    zipf.writestr(file_path, content)

            # Step 4: Complete
            self.generation_status[project_id].status = "completed"
            self.generation_status[project_id].phase = "completed"
            self.generation_status[project_id].current_step = (
                "Project generation completed"
            )
            self.generation_status[project_id].progress = 100
            self.generation_status[project_id].can_modify = True

            return IntelligentProjectResponse(
                project_id=project_id,
                status="completed",
                message="Project generated successfully",
                analysis=analysis,
                download_url=f"/api/download/{project_id}",
                generated_files=list(project_files.keys()),
            )

        except Exception as e:
            self.generation_status[project_id].status = "failed"
            self.generation_status[project_id].error_message = str(e)
            self.generation_status[project_id].current_step = f"Failed: {str(e)}"

            raise Exception(f"Project generation failed: {str(e)}")

    def get_enhanced_generation_status(
        self, project_id: str
    ) -> Optional[EnhancedGenerationStatus]:
        """Get the enhanced generation status for a project"""
        return self.generation_status.get(project_id)

    def get_project_analysis(self, project_id: str) -> Optional[ProjectAnalysis]:
        """Get the project analysis for a project"""
        return self.project_analyses.get(project_id)

    def get_project_files(self, project_id: str) -> Optional[Dict[str, str]]:
        """Get the project files for a project"""
        return self.project_files.get(project_id)

    def _create_basic_analysis(
        self, request: IntelligentProjectRequest
    ) -> ProjectAnalysis:
        """Create a basic analysis when AI fails"""
        from app.models.project_models import TechnologyStack

        return ProjectAnalysis(
            project_type="fullstack",
            suggested_name=request.project_name or "ai-generated-project",
            technology_stack=TechnologyStack(
                frontend=["React", "TypeScript", "Vite"],
                backend=["FastAPI", "Python"],
                database=["SQLite"],
                styling=["Tailwind CSS"],
                testing=["Vitest", "Jest"],
            ),
            architecture_pattern="MVC",
            estimated_complexity=request.complexity_level,
            key_features=["Modern UI", "Responsive Design", "API Ready"],
            technical_requirements=["Modern web standards"],
            suggested_folder_structure={},
            dependencies={
                "frontend": ["react", "typescript", "vite", "tailwindcss"],
                "backend": ["fastapi", "uvicorn"],
            },
            reasoning="Basic analysis created due to AI response parsing issues",
        )

    def _flatten_dependencies(
        self, dependencies: Dict[str, Any]
    ) -> Dict[str, List[str]]:
        """Flatten nested dependency structures to match expected format"""
        flattened = {}

        for key, value in dependencies.items():
            if isinstance(value, list):
                # Already in correct format
                flattened[key] = value
            elif isinstance(value, dict):
                # Nested structure - flatten it
                for sub_key, sub_value in value.items():
                    if isinstance(sub_value, list):
                        # Create a new key combining the parent and child keys
                        new_key = (
                            f"{key}_{sub_key}"
                            if key != "testing"
                            else f"{sub_key}_testing"
                        )
                        flattened[new_key] = sub_value
                    else:
                        # Convert single values to list
                        new_key = (
                            f"{key}_{sub_key}"
                            if key != "testing"
                            else f"{sub_key}_testing"
                        )
                        flattened[new_key] = [str(sub_value)]
            else:
                # Single value - convert to list
                flattened[key] = [str(value)]

        return flattened

    def _process_technology_stack(
        self, tech_stack_data: Dict[str, Any]
    ) -> Dict[str, List[str]]:
        """Process and flatten technology stack data to ensure all fields are lists"""
        processed = {}

        # Define all possible fields for TechnologyStack
        tech_fields = [
            "frontend",
            "backend",
            "database",
            "deployment",
            "testing",
            "styling",
            "state_management",
            "authentication",
            "additional_tools",
        ]

        for field in tech_fields:
            if field in tech_stack_data:
                value = tech_stack_data[field]

                if isinstance(value, list):
                    # Already a list, just ensure all items are strings
                    processed[field] = [str(item) for item in value]
                elif isinstance(value, str):
                    # Single string, convert to list
                    if "," in value:
                        # Split by comma if it contains multiple items
                        processed[field] = [
                            v.strip() for v in value.split(",") if v.strip()
                        ]
                    else:
                        processed[field] = [value.strip()]
                elif isinstance(value, dict):
                    # Nested object, flatten it
                    flattened_items = []
                    for k, v in value.items():
                        if isinstance(v, str):
                            flattened_items.append(f"{k}: {v}")
                        elif isinstance(v, list):
                            flattened_items.extend([f"{k}: {item}" for item in v])
                        else:
                            flattened_items.append(f"{k}: {str(v)}")
                    processed[field] = flattened_items
                else:
                    # Any other type, convert to string and put in list
                    processed[field] = [str(value)]

        return processed

    def _extract_json_from_response(self, response: str) -> str:
        """Extract JSON from AI response, handling markdown code blocks and explanatory text"""
        response = response.strip()

        # Remove common prefixes that AI might add
        prefixes_to_remove = [
            "Here is the JSON object",
            "Below is the JSON object",
            "The JSON object is",
            "Here's the JSON",
            "Below is the",
            "The following is",
        ]

        for prefix in prefixes_to_remove:
            if response.lower().startswith(prefix.lower()):
                # Find the first { after the prefix
                first_brace = response.find("{")
                if first_brace != -1:
                    response = response[first_brace:]
                break

        # Look for JSON content between ```json and ```
        json_start = response.find("```json")
        if json_start != -1:
            json_start = response.find("{", json_start)
            if json_start != -1:
                remaining = response[json_start:]
                # Find the closing ``` after the opening brace
                closing_markers = remaining.find("```")
                if closing_markers != -1:
                    json_content = remaining[:closing_markers]
                    json_end = json_content.rfind("}")
                    if json_end != -1:
                        return json_content[: json_end + 1]
                else:
                    # No closing ```, find the last }
                    json_end = remaining.rfind("}")
                    if json_end != -1:
                        return remaining[: json_end + 1]

        # Look for JSON content between ``` and ``` (without json specifier)
        json_start = response.find("```")
        if json_start != -1:
            json_start = response.find("{", json_start)
            if json_start != -1:
                remaining = response[json_start:]
                closing_markers = remaining.find("```")
                if closing_markers != -1:
                    json_content = remaining[:closing_markers]
                    json_end = json_content.rfind("}")
                    if json_end != -1:
                        return json_content[: json_end + 1]

        # Look for JSON content between { and } (fallback)
        json_start = response.find("{")
        json_end = response.rfind("}")

        if json_start != -1 and json_end != -1 and json_end > json_start:
            return response[json_start : json_end + 1]

        return response

    async def suggest_modifications(
        self,
        project_id: str,
        modification_prompt: str,
        current_files: Dict[str, str],
        ai_provider: AIProvider = AIProvider.OPENAI,
    ) -> List[ModificationSuggestion]:
        """Generate modification suggestions using AI"""

        suggestion_prompt = f"""
        Based on the current project files and the modification request, suggest specific improvements.

        MODIFICATION REQUEST: {modification_prompt}

        CURRENT FILES: {len(current_files)} files in the project

        Provide 3-5 specific modification suggestions in JSON format:
        {{
            "suggestions": [
                {{
                    "suggestion_id": "unique_id",
                    "title": "Brief title",
                    "description": "Detailed description",
                    "category": "feature|bugfix|improvement|refactor",
                    "files": [
                        {{
                            "file_path": "path/to/file",
                            "action": "create|modify|delete",
                            "content": "file content if creating/modifying"
                        }}
                    ],
                    "estimated_effort": "small|medium|large",
                    "impact": "low|medium|high",
                    "reasoning": "Why this suggestion is valuable"
                }}
            ]
        }}

        Return ONLY valid JSON.
        """

        messages = [
            SystemMessage(
                content="You are an expert developer providing code improvement suggestions."
            ),
            HumanMessage(content=suggestion_prompt),
        ]

        try:
            print(f"DEBUG: Modification suggestions using AI provider: {ai_provider}")
            response_content = await self._call_ai_provider(messages, ai_provider)
        except Exception as e:
            print(f"WARNING: Modification AI provider {ai_provider} failed: {e}")
            raise Exception(f"Failed to generate modification suggestions: {str(e)}")

        try:
            data = json.loads(response_content)
            suggestions = []

            for suggestion_data in data.get("suggestions", []):
                files = []
                for file_data in suggestion_data.get("files", []):
                    files.append(FileModification(**file_data))

                suggestion = ModificationSuggestion(
                    suggestion_id=suggestion_data.get(
                        "suggestion_id", str(uuid.uuid4())
                    ),
                    title=suggestion_data.get("title", "Modification"),
                    description=suggestion_data.get("description", ""),
                    category=suggestion_data.get("category", "feature"),
                    files=files,
                    estimated_effort=suggestion_data.get("estimated_effort", "medium"),
                    impact=suggestion_data.get("impact", "medium"),
                    reasoning=suggestion_data.get("reasoning", ""),
                )
                suggestions.append(suggestion)

            return suggestions
        except Exception as e:
            raise Exception(f"Failed to generate modification suggestions: {str(e)}")

    async def generate_project(
        self, request: IntelligentProjectRequest
    ) -> Dict[str, Any]:
        """Generate a complete project using simplified workflow"""

        # Create initial state
        state = {
            "messages": [],
            "request": request,
            "analysis": None,
            "project_structure": {},
            "generated_files": {},
            "validation_results": {},
            "current_step": "Starting project generation",
            "progress": 0,
            "error_message": "",
        }

        # Run simplified workflow steps
        state = await self._analyze_requirements(state)
        state = await self._design_architecture(state)
        state = await self._generate_structure(state)
        state = await self._generate_frontend(state)
        state = await self._generate_backend(state)
        state = await self._generate_config(state)
        state = await self._validate_project(state)
        state = await self._finalize_project(state)

        return {
            "files": state["generated_files"],
            "analysis": state["analysis"],
            "structure": state["project_structure"],
            "validation": state["validation_results"],
            "instructions": self._generate_setup_instructions(state["analysis"]),
            "next_steps": self._generate_next_steps(state["analysis"]),
        }

    async def _analyze_requirements(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze project requirements from the user prompt"""

        state["current_step"] = "Analyzing project requirements"
        state["progress"] = 10

        analysis_prompt = f"""
        You are an expert software architect. Analyze this project request and provide a comprehensive technical analysis.
        
        PROJECT REQUEST: {state["request"].prompt}
        
        USER PREFERENCES:
        - Preferred Technologies: {state["request"].preferred_technologies or 'None'}
        - Target Platforms: {state["request"].target_platform or 'Not specified'}
        - Complexity Level: {state["request"].complexity_level}
        - Include Tests: {state["request"].include_tests}
        - Include Documentation: {state["request"].include_documentation}
        
        Provide analysis in JSON format with:
        - project_type (fullstack/frontend/backend/mobile/api)
        - suggested_name
        - technology_stack (frontend, backend, database, styling, testing)
        - architecture_pattern
        - estimated_complexity
        - key_features (list)
        - technical_requirements (list)
        - dependencies
        - reasoning
        
        Return ONLY valid JSON.
        """

        messages = [
            SystemMessage(
                content="You are an expert software architect providing technical analysis."
            ),
            HumanMessage(content=analysis_prompt),
        ]

        try:
            print(f"DEBUG: Workflow using AI provider: {state['request'].ai_provider}")
            response_content = await self._call_ai_provider(
                messages, state["request"].ai_provider
            )
        except Exception as e:
            print(
                f"WARNING: Workflow AI provider {state['request'].ai_provider} failed: {e}"
            )
            print("Falling back to basic analysis")
            analysis = self._create_basic_analysis(state["request"])
            state["analysis"] = analysis
            state["messages"].append(
                HumanMessage(
                    content=f"Analysis completed (basic): {analysis.suggested_name}"
                )
            )
            return state

        try:
            # Debug: Print the raw response
            print(f"DEBUG: Workflow AI response: {response_content[:500]}...")

            # Try to extract JSON from the response
            response_text = response_content.strip()

            # Extract JSON from response using improved method
            json_content = self._extract_json_from_response(response_text)

            if not json_content or json_content == response_text:
                print(
                    "WARNING: No JSON found in workflow AI response, creating basic analysis"
                )
                analysis = self._create_basic_analysis(state["request"])
            else:
                analysis_data = json.loads(json_content)

                # Create TechnologyStack - handle both string and list formats
                tech_stack_data = analysis_data.get("technology_stack", {})

                # Process and flatten technology stack data
                processed_tech_stack = self._process_technology_stack(tech_stack_data)

                technology_stack = TechnologyStack(**processed_tech_stack)

                # Create ProjectAnalysis
                # Ensure reasoning is a string
                reasoning = analysis_data.get("reasoning", "AI analysis completed")
                if not isinstance(reasoning, str):
                    reasoning = str(reasoning) if reasoning else "AI analysis completed"

                analysis = ProjectAnalysis(
                    project_type=analysis_data.get("project_type", "fullstack"),
                    suggested_name=analysis_data.get(
                        "suggested_name", state["request"].project_name or "ai-project"
                    ),
                    technology_stack=technology_stack,
                    architecture_pattern=analysis_data.get(
                        "architecture_pattern", "MVC"
                    ),
                    estimated_complexity=analysis_data.get(
                        "estimated_complexity", state["request"].complexity_level
                    ),
                    key_features=analysis_data.get("key_features", []),
                    technical_requirements=analysis_data.get(
                        "technical_requirements", []
                    ),
                    suggested_folder_structure=analysis_data.get(
                        "suggested_folder_structure", {}
                    ),
                    dependencies=self._flatten_dependencies(
                        analysis_data.get("dependencies", {})
                    ),
                    reasoning=reasoning,
                )

            state["analysis"] = analysis
            state["messages"].append(
                HumanMessage(content=f"Analysis completed: {analysis.suggested_name}")
            )

        except json.JSONDecodeError as e:
            print(f"WARNING: Workflow JSON parsing failed: {e}")
            analysis = self._create_basic_analysis(state["request"])
            state["analysis"] = analysis
            state["messages"].append(
                HumanMessage(
                    content=f"Analysis completed (basic): {analysis.suggested_name}"
                )
            )
        except Exception as e:
            print(f"ERROR: Workflow analysis failed: {e}")
            analysis = self._create_basic_analysis(state["request"])
            state["analysis"] = analysis
            state["messages"].append(
                HumanMessage(
                    content=f"Analysis completed (basic): {analysis.suggested_name}"
                )
            )

        return state

    async def _design_architecture(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Design the project architecture and folder structure"""

        state["current_step"] = "Designing project architecture"
        state["progress"] = 20

        analysis = state["analysis"]

        # Determine project structure based on analysis
        needs_frontend = (
            analysis.project_type in ["fullstack", "frontend"]
            or analysis.technology_stack.frontend
        )
        needs_backend = (
            analysis.project_type in ["fullstack", "backend", "api"]
            or analysis.technology_stack.backend
        )

        project_structure = {
            "type": analysis.project_type,
            "name": analysis.suggested_name,
            "needs_frontend": needs_frontend,
            "needs_backend": needs_backend,
            "architecture": analysis.architecture_pattern,
            "folders": {},
        }

        if needs_frontend:
            project_structure["folders"]["frontend"] = {
                "src": ["components", "pages", "services", "hooks", "utils", "types"],
                "public": ["assets", "icons"],
                "config": ["vite.config.ts", "tailwind.config.js", "tsconfig.json"],
            }

        if needs_backend:
            project_structure["folders"]["backend"] = {
                "app": ["models", "services", "routes", "utils", "middleware"],
                "tests": ["unit", "integration"],
                "config": ["requirements.txt", "main.py", ".env.example"],
            }

        # Add root level files
        project_structure["folders"]["root"] = [
            "README.md",
            ".gitignore",
            "docker-compose.yml",
        ]

        state["project_structure"] = project_structure
        state["messages"].append(
            HumanMessage(
                content=f"Architecture designed: {analysis.architecture_pattern}"
            )
        )

        return state

    async def _generate_structure(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Generate the basic project structure and configuration files"""

        state["current_step"] = "Generating project structure"
        state["progress"] = 30

        analysis = state["analysis"]
        structure = state["project_structure"]
        files = {}

        # Generate README.md
        files["README.md"] = self._generate_readme(analysis)

        # Generate .gitignore
        files[".gitignore"] = self._generate_gitignore(structure)

        # Generate docker-compose.yml if needed
        if structure["needs_frontend"] and structure["needs_backend"]:
            files["docker-compose.yml"] = self._generate_docker_compose(analysis)

        state["generated_files"].update(files)
        state["messages"].append(
            HumanMessage(content=f"Generated {len(files)} structure files")
        )

        return state

    async def _generate_frontend(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Generate frontend files and components"""

        state["current_step"] = "Generating frontend components"
        state["progress"] = 50

        analysis = state["analysis"]
        structure = state["project_structure"]

        if not structure["needs_frontend"]:
            return state

        # Generate frontend files using AI
        frontend_files = await self._generate_ai_frontend(analysis, state["request"])
        state["generated_files"].update(frontend_files)
        state["messages"].append(
            HumanMessage(content=f"Generated {len(frontend_files)} frontend files")
        )

        return state

    async def _generate_backend(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Generate backend files and API endpoints"""

        state["current_step"] = "Generating backend API"
        state["progress"] = 70

        analysis = state["analysis"]
        structure = state["project_structure"]

        if not structure["needs_backend"]:
            return state

        # Generate backend files using AI
        backend_files = await self._generate_ai_backend(analysis, state["request"])
        state["generated_files"].update(backend_files)
        state["messages"].append(
            HumanMessage(content=f"Generated {len(backend_files)} backend files")
        )

        return state

    async def _generate_config(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Generate configuration and deployment files"""

        state["current_step"] = "Generating configuration files"
        state["progress"] = 85

        analysis = state["analysis"]
        structure = state["project_structure"]

        config_files = {}

        # Generate deployment configurations
        if structure["needs_frontend"] and structure["needs_backend"]:
            config_files["Makefile"] = self._generate_makefile(analysis)

        # Generate CI/CD if requested
        if state["request"].include_ci_cd:
            config_files[".github/workflows/ci.yml"] = self._generate_github_actions(
                analysis
            )

        # Generate environment files
        config_files[".env.example"] = self._generate_env_example(analysis)

        state["generated_files"].update(config_files)
        state["messages"].append(
            HumanMessage(content=f"Generated {len(config_files)} config files")
        )

        return state

    async def _validate_project(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Validate the generated project structure and files"""

        state["current_step"] = "Validating project structure"
        state["progress"] = 95

        validation_results = {
            "total_files": len(state["generated_files"]),
            "has_frontend": any(
                path.startswith("frontend/") for path in state["generated_files"]
            ),
            "has_backend": any(
                path.startswith("backend/") for path in state["generated_files"]
            ),
            "has_readme": "README.md" in state["generated_files"],
            "has_gitignore": ".gitignore" in state["generated_files"],
            "missing_files": [],
            "validation_passed": True,
        }

        # Check for essential files
        essential_files = ["README.md"]

        if state["project_structure"]["needs_frontend"]:
            essential_files.extend(
                [
                    "frontend/package.json",
                    "frontend/src/App.tsx",
                    "frontend/src/main.tsx",
                ]
            )

        if state["project_structure"]["needs_backend"]:
            essential_files.extend(["backend/requirements.txt", "backend/main.py"])

        for file in essential_files:
            if file not in state["generated_files"]:
                validation_results["missing_files"].append(file)
                validation_results["validation_passed"] = False

        state["validation_results"] = validation_results
        state["messages"].append(
            HumanMessage(
                content=f"Validation completed: {validation_results['validation_passed']}"
            )
        )

        return state

    async def _finalize_project(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Finalize the project generation"""

        state["current_step"] = "Project generation completed"
        state["progress"] = 100

        # Add final summary
        summary = {
            "project_name": state["analysis"].suggested_name,
            "project_type": state["analysis"].project_type,
            "total_files": len(state["generated_files"]),
            "technologies": {
                "frontend": state["analysis"].technology_stack.frontend,
                "backend": state["analysis"].technology_stack.backend,
                "database": state["analysis"].technology_stack.database,
            },
            "validation_passed": state["validation_results"].get(
                "validation_passed", False
            ),
        }

        state["messages"].append(
            HumanMessage(content=f"Project generation completed: {summary}")
        )

        return state

    # Helper methods for generating file content

    def _generate_readme(self, analysis: ProjectAnalysis) -> str:
        """Generate README.md content"""
        return f"""# {analysis.suggested_name}

## Description
{analysis.reasoning}

## Features
{chr(10).join(f"- {feature}" for feature in analysis.key_features)}

## Technology Stack
- **Frontend**: {', '.join(analysis.technology_stack.frontend or [])}
- **Backend**: {', '.join(analysis.technology_stack.backend or [])}
- **Database**: {', '.join(analysis.technology_stack.database or [])}
- **Styling**: {', '.join(analysis.technology_stack.styling or [])}

## Architecture
- **Pattern**: {analysis.architecture_pattern}
- **Complexity**: {analysis.estimated_complexity}

## Setup Instructions

### Prerequisites
- Node.js 18+ (for frontend)
- Python 3.9+ (for backend)
- Git

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd {analysis.suggested_name}
```

2. Install frontend dependencies:
```bash
cd frontend
npm install
```

3. Install backend dependencies:
```bash
cd ../backend
pip install -r requirements.txt
```

4. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your configuration
```

### Running the Application

1. Start the backend server:
```bash
cd backend
python main.py
```

2. Start the frontend development server:
```bash
cd frontend
npm run dev
```

3. Open your browser and navigate to `http://localhost:3000`

## Project Structure
```
{analysis.suggested_name}/
├── frontend/          # React frontend application
│   ├── src/
│   │   ├── components/
│   │   ├── pages/
│   │   └── services/
│   └── package.json
├── backend/           # FastAPI backend application
│   ├── app/
│   │   ├── models/
│   │   ├── routes/
│   │   └── services/
│   └── requirements.txt
└── README.md
```

## Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## License
MIT License

---
Generated by AI Project Generator
"""

    def _generate_gitignore(self, structure: Dict[str, Any]) -> str:
        """Generate .gitignore content"""
        gitignore_content = """# Dependencies
node_modules/
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
env/
venv/
.venv/
pip-log.txt
pip-delete-this-directory.txt

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build outputs
dist/
build/
*.tsbuildinfo

# Database
*.db
*.sqlite
*.sqlite3

# Temporary files
tmp/
temp/
"""

        if structure.get("needs_frontend"):
            gitignore_content += """
# Frontend specific
frontend/dist/
frontend/build/
frontend/.next/
frontend/out/
"""

        if structure.get("needs_backend"):
            gitignore_content += """
# Backend specific
backend/__pycache__/
backend/*.pyc
backend/.pytest_cache/
backend/instance/
backend/.coverage
backend/htmlcov/
"""

        return gitignore_content

    async def _generate_ai_frontend(
        self, analysis: ProjectAnalysis, request: IntelligentProjectRequest = None
    ) -> Dict[str, str]:
        """Generate frontend files using AI based on analysis"""

        frontend_prompt = f"""
        Generate a complete React frontend application based on this analysis:

        Project: {analysis.suggested_name}
        Type: {analysis.project_type}
        Features: {', '.join(analysis.key_features)}
        Tech Stack: {', '.join(analysis.technology_stack.frontend or [])}
        Styling: {', '.join(analysis.technology_stack.styling or [])}

        Generate the following files with complete, production-ready code:
        1. package.json - with all necessary dependencies
        2. vite.config.ts - Vite configuration
        3. index.html - main HTML file
        4. src/main.tsx - React entry point
        5. src/App.tsx - main App component with routing
        6. src/components/Header.tsx - header component
        7. src/components/MainContent.tsx - main content component
        8. src/components/Footer.tsx - footer component
        9. src/index.css - global styles with Tailwind
        10. tailwind.config.js - Tailwind configuration
        11. tsconfig.json - TypeScript configuration
        12. postcss.config.js - PostCSS configuration

        Requirements:
        - Use React 18 with TypeScript
        - Use Vite as build tool
        - Use Tailwind CSS for styling
        - Include React Router for navigation
        - Make it responsive and modern
        - Include proper TypeScript types
        - Add loading states and error handling

        Return as JSON with file paths as keys and file contents as values.
        """

        messages = [
            SystemMessage(
                content="You are an expert React developer. Generate complete, production-ready frontend code."
            ),
            HumanMessage(content=frontend_prompt),
        ]

        try:
            print(
                f"DEBUG: Frontend generation using AI provider: {request.ai_provider}"
            )
            response_content = await self._call_ai_provider(
                messages, request.ai_provider
            )
        except Exception as e:
            print(f"WARNING: Frontend AI provider {request.ai_provider} failed: {e}")
            print("Falling back to basic frontend files")
            return self._create_basic_frontend_files(analysis)

        try:
            # Debug: Print the raw response
            print(f"DEBUG: Frontend AI response: {response_content[:200]}...")

            # Try to extract JSON from the response
            response_text = response_content.strip()

            # Extract JSON from response using improved method
            json_content = self._extract_json_from_response(response_text)

            if not json_content or json_content == response_text:
                print(
                    "WARNING: No JSON found in frontend AI response, creating basic files"
                )
                return self._create_basic_frontend_files(analysis)

            files_data = json.loads(json_content)

            # Validate that we have some files
            if not files_data or len(files_data) < 3:
                print("WARNING: Insufficient files from AI, creating basic files")
                return self._create_basic_frontend_files(analysis)

            return files_data
        except json.JSONDecodeError as e:
            print(f"WARNING: Frontend JSON parsing failed: {e}")
            return self._create_basic_frontend_files(analysis)
        except Exception as e:
            print(f"ERROR: Frontend generation failed: {e}")
            return self._create_basic_frontend_files(analysis)

    def _create_basic_frontend_files(self, analysis: ProjectAnalysis) -> Dict[str, str]:
        """Create basic frontend files when AI fails"""
        project_name = analysis.suggested_name

        return {
            "frontend/package.json": f"""{{"name": "{project_name}-frontend",
  "version": "1.0.0",
  "type": "module",
  "scripts": {{
    "dev": "vite",
    "build": "vite build",
    "preview": "vite preview"
  }},
  "dependencies": {{
    "react": "^18.2.0",
    "react-dom": "^18.2.0"
  }},
  "devDependencies": {{
    "@types/react": "^18.2.0",
    "@types/react-dom": "^18.2.0",
    "@vitejs/plugin-react": "^4.0.0",
    "typescript": "^5.0.2",
    "vite": "^4.3.0"
  }}
}}""",
            "frontend/index.html": f"""<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>{project_name}</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>""",
            "frontend/src/main.tsx": """import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App'

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)""",
            "frontend/src/App.tsx": f"""import React from 'react'

function App() {{
  return (
    <div>
      <h1>Welcome to {project_name}</h1>
      <p>Your AI-generated project is ready!</p>
    </div>
  )
}}

export default App""",
            "frontend/vite.config.ts": """import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000
  }
})""",
        }

    async def _generate_ai_backend(
        self, analysis: ProjectAnalysis, request: IntelligentProjectRequest = None
    ) -> Dict[str, str]:
        """Generate backend files using AI based on analysis"""

        backend_prompt = f"""
        Generate a complete FastAPI backend application based on this analysis:

        Project: {analysis.suggested_name}
        Type: {analysis.project_type}
        Features: {', '.join(analysis.key_features)}
        Tech Stack: {', '.join(analysis.technology_stack.backend or [])}
        Database: {', '.join(analysis.technology_stack.database or [])}

        Generate the following files with complete, production-ready code:
        1. requirements.txt - with all necessary dependencies
        2. main.py - FastAPI application with CORS and basic endpoints
        3. app/__init__.py - empty init file
        4. app/models.py - SQLAlchemy models
        5. app/database.py - database configuration
        6. app/schemas.py - Pydantic schemas
        7. .env.example - environment variables template
        8. Dockerfile - for containerization

        Requirements:
        - Use FastAPI with Python 3.11+
        - Include SQLAlchemy for database ORM
        - Use Pydantic for data validation
        - Include CORS middleware for frontend integration
        - Add proper error handling
        - Include health check endpoint
        - Make it production-ready
        - Include proper typing

        Return as JSON with file paths as keys and file contents as values.
        """

        messages = [
            SystemMessage(
                content="You are an expert Python/FastAPI developer. Generate complete, production-ready backend code."
            ),
            HumanMessage(content=backend_prompt),
        ]

        try:
            print(f"DEBUG: Backend generation using AI provider: {request.ai_provider}")
            response_content = await self._call_ai_provider(
                messages, request.ai_provider
            )
        except Exception as e:
            print(f"WARNING: Backend AI provider {request.ai_provider} failed: {e}")
            print("Falling back to basic backend files")
            return self._create_basic_backend_files(analysis)

        try:
            # Debug: Print the raw response
            print(f"DEBUG: Backend AI response: {response_content[:200]}...")

            # Try to extract JSON from the response
            response_text = response_content.strip()

            # Extract JSON from response using improved method
            json_content = self._extract_json_from_response(response_text)

            if not json_content or json_content == response_text:
                print(
                    "WARNING: No JSON found in backend AI response, creating basic files"
                )
                return self._create_basic_backend_files(analysis)

            files_data = json.loads(json_content)

            # Validate that we have some files
            if not files_data or len(files_data) < 2:
                print(
                    "WARNING: Insufficient backend files from AI, creating basic files"
                )
                return self._create_basic_backend_files(analysis)

            return files_data
        except json.JSONDecodeError as e:
            print(f"WARNING: Backend JSON parsing failed: {e}")
            return self._create_basic_backend_files(analysis)
        except Exception as e:
            print(f"ERROR: Backend generation failed: {e}")
            return self._create_basic_backend_files(analysis)

    def _create_basic_backend_files(self, analysis: ProjectAnalysis) -> Dict[str, str]:
        """Create basic backend files when AI fails"""
        project_name = analysis.suggested_name

        return {
            "backend/requirements.txt": """fastapi==0.104.1
uvicorn[standard]==0.24.0
python-dotenv==1.0.0""",
            "backend/main.py": f"""from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

app = FastAPI(title="{project_name} API")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {{"message": "Welcome to {project_name} API"}}

@app.get("/health")
async def health():
    return {{"status": "healthy"}}
""",
            "backend/.env.example": f"""# {project_name} Environment Variables
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=True
""",
        }

    def _generate_docker_compose(self, analysis: ProjectAnalysis) -> str:
        """Generate docker-compose.yml"""
        project_name = analysis.suggested_name.lower().replace(" ", "-")

        return f"""version: '3.8'

services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8000
    depends_on:
      - backend
    volumes:
      - ./frontend:/app
      - /app/node_modules

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**************************************/{project_name}
    depends_on:
      - db
    volumes:
      - ./backend:/app

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB={project_name}
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
"""

    def _generate_makefile(self, analysis: ProjectAnalysis) -> str:
        """Generate Makefile for project management"""
        return """# Project Management Makefile

.PHONY: help install dev build test clean docker-up docker-down

help:
	@echo "Available commands:"
	@echo "  install     - Install all dependencies"
	@echo "  dev         - Start development servers"
	@echo "  build       - Build the project"
	@echo "  test        - Run tests"
	@echo "  clean       - Clean build artifacts"
	@echo "  docker-up   - Start with Docker Compose"
	@echo "  docker-down - Stop Docker Compose"

install:
	@echo "Installing frontend dependencies..."
	cd frontend && npm install
	@echo "Installing backend dependencies..."
	cd backend && pip install -r requirements.txt

dev:
	@echo "Starting development servers..."
	@echo "Backend will run on http://localhost:8000"
	@echo "Frontend will run on http://localhost:3000"
	@make -j2 dev-backend dev-frontend

dev-backend:
	cd backend && python main.py

dev-frontend:
	cd frontend && npm run dev

build:
	@echo "Building frontend..."
	cd frontend && npm run build
	@echo "Build complete!"

test:
	@echo "Running frontend tests..."
	cd frontend && npm test
	@echo "Running backend tests..."
	cd backend && python -m pytest

clean:
	@echo "Cleaning build artifacts..."
	rm -rf frontend/dist
	rm -rf frontend/build
	rm -rf backend/__pycache__
	rm -rf backend/.pytest_cache

docker-up:
	docker-compose up -d

docker-down:
	docker-compose down
"""

    def _generate_env_example(self, analysis: ProjectAnalysis) -> str:
        """Generate .env.example file"""
        return f"""# {analysis.suggested_name} Environment Variables

# Development
NODE_ENV=development
DEBUG=true

# API Configuration
API_URL=http://localhost:8000
API_TIMEOUT=30000

# Database
DATABASE_URL=sqlite:///./app.db

# Security
JWT_SECRET=your-jwt-secret-here
ENCRYPTION_KEY=your-encryption-key-here

# External Services
# Add your API keys and service configurations here
"""

    def _generate_github_actions(self, analysis: ProjectAnalysis) -> str:
        """Generate GitHub Actions CI/CD workflow"""
        return f"""name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install frontend dependencies
      run: |
        cd frontend
        npm ci

    - name: Install backend dependencies
      run: |
        cd backend
        pip install -r requirements.txt

    - name: Run frontend tests
      run: |
        cd frontend
        npm test

    - name: Run backend tests
      run: |
        cd backend
        python -m pytest

    - name: Build frontend
      run: |
        cd frontend
        npm run build

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v3

    - name: Deploy to production
      run: |
        echo "Add your deployment steps here"
        # Example: Deploy to your hosting service
"""

    def _generate_setup_instructions(self, analysis: ProjectAnalysis) -> List[str]:
        """Generate setup instructions for the project"""
        instructions = [
            "1. Clone or download the generated project files",
            "2. Navigate to the project directory",
            "3. Install frontend dependencies: cd frontend && npm install",
            "4. Install backend dependencies: cd backend && pip install -r requirements.txt",
            "5. Copy .env.example to .env and configure your environment variables",
            "6. Start the backend server: cd backend && python main.py",
            "7. Start the frontend development server: cd frontend && npm run dev",
            "8. Open your browser and navigate to http://localhost:3000",
        ]

        if (
            analysis.technology_stack.database
            and "postgresql" in str(analysis.technology_stack.database).lower()
        ):
            instructions.insert(
                5, "5a. Set up PostgreSQL database and update DATABASE_URL in .env"
            )

        return instructions

    def _generate_next_steps(self, analysis: ProjectAnalysis) -> List[str]:
        """Generate next steps for project development"""
        return [
            "🎯 Customize the UI components to match your design requirements",
            "🔧 Add your specific business logic to the backend API endpoints",
            "🗄️ Set up your database schema and models",
            "🔐 Implement authentication and authorization if needed",
            "📝 Add comprehensive tests for your components and API endpoints",
            "🚀 Configure deployment to your preferred hosting platform",
            "📚 Update documentation with your specific requirements",
            "🔍 Add monitoring and logging for production use",
        ]
