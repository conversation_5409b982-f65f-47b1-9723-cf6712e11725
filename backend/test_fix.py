#!/usr/bin/env python3
"""
Simple test to verify the dependency flattening fix
"""

import json
from app.services.intelligent_ai_service import IntelligentAIService
from app.models.project_models import IntelligentProjectRequest, AIProvider

def test_problematic_response():
    """Test the exact problematic response that was causing the validation error"""
    
    # This is the problematic AI response structure that was causing the error
    problematic_response = """```json
{
  "project_type": "fullstack",
  "suggested_name": "FastDash",
  "technology_stack": {
    "frontend": ["React.js", "TypeScript"],
    "backend": ["Python", "FastAPI"],
    "database": ["MongoDB"],
    "testing": ["Jest", "Pytest"]
  },
  "architecture_pattern": "Model-View-Controller (MVC)",
  "estimated_complexity": "medium",
  "key_features": ["Dashboard", "Real-time updates"],
  "technical_requirements": ["Modern web standards"],
  "suggested_folder_structure": {},
  "dependencies": {
    "frontend": ["react", "typescript", "vite"],
    "backend": ["fastapi", "uvicorn"],
    "testing": {
      "frontend": ["jest", "react-testing-library"],
      "backend": ["pytest", "pytest-asyncio", "httpx"]
    }
  },
  "reasoning": "FastAPI provides excellent performance for backend APIs"
}
```"""

    service = IntelligentAIService()
    request = IntelligentProjectRequest(
        prompt="Create a dashboard application",
        ai_provider=AIProvider.OPENAI
    )
    
    try:
        # This should now work without validation errors
        analysis = service._parse_analysis_response(problematic_response, request)
        print("✅ SUCCESS: Analysis parsed without validation errors")
        print(f"Project name: {analysis.suggested_name}")
        print(f"Dependencies: {analysis.dependencies}")
        
        # Verify all dependency values are lists of strings
        for key, value in analysis.dependencies.items():
            if not isinstance(value, list):
                print(f"❌ ERROR: {key} is not a list: {type(value)}")
                return False
            if not all(isinstance(item, str) for item in value):
                print(f"❌ ERROR: {key} contains non-string items: {value}")
                return False
        
        print("✅ All dependency values are valid lists of strings")
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

if __name__ == "__main__":
    success = test_problematic_response()
    if success:
        print("\n🎉 Fix verified successfully!")
    else:
        print("\n💥 Fix failed!")
